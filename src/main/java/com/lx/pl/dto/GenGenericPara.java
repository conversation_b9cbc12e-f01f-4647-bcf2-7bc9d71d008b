package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@ToString
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GenGenericPara {

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "模型id")
    private String model_id;

    @Schema(description = "正向提示词")
    private String prompt;

    @Schema(description = "反向提示词")
    private String negative_prompt;

    @Schema(description = "批量生成信息")
    private Resolution resolution;

    @Schema(description = "超清修复")
    private Hires_fix hires_fix;

    @Schema(description = "去除背景")
    private Rembg rembg;

    @Schema(description = "动漫化指数")
    private Model_ability model_ability;

    @Schema(description = "种子数，10位整数")
    private Long seed;

    @Schema(description = "迭代步数，默认20步，1-60步之间")
    private Integer steps;

    @Schema(description = "提示词引导系数，默认5.0，1.0-30.0之间")
    private Double cfg;

    @Schema(description = "采样器，默认选euler_ancestral，可选项：[\"euler\", \"euler_ancestral\", \"heun\", \"heunpp2\",\"dpm_2\", \"dpm_2_ancestral\",\"lms\", \"dpm_fast\", \"dpm_adaptive\", \"dpmpp_2s_ancestral\", \"dpmpp_sde\", \"dpmpp_sde_gpu\", \"dpmpp_2m\", \"dpmpp_2m_sde\", \"dpmpp_2m_sde_gpu\", \"dpmpp_3m_sde\", \"dpmpp_3m_sde_gpu\", \"ddpm\", \"lcm\"]")
    private String sampler_name;

    @Schema(description = "调度器，默认选karras，可选项：[\"normal\", \"karras\", \"exponential\", \"sgm_uniform\", \"simple\", \"ddim_uniform\"]")
    private String scheduler;

    @Schema(description = "降噪幅度，0.00-1.00之间")
    private double denoise;

    @Schema(description = "生图模式:(fast ： 快速生图   quality ： 高质量生图)")
    private String gen_mode;

    @Schema(description = "图生图参数")
    private Img2img_info img2img_info;

    @Schema(description = "多图生图参数")
    private Multi_img2img_info multi_img2img_info;

    @Schema(description = "图片控制参数")
    private Img_control_info img_control_info;

    @Schema(description = "局部重绘入参")
    private GenLocalRedrawPara genLocalRedrawPara;

    @Schema(description = "局部扩图入参")
    private EnlargeImagePara enlargeImagePara;

    @Schema(description = "线稿上色")
    private LineRecolorPara lineRecolorPara;

    @Schema(description = "图片微变")
    private VaryPara varyPara;

    @Schema(description = "图片编辑")
    private ImgEditPara imgEditPara;

    @Schema(description = "多图片编辑")
    private List<ImgEditPara> multiImgEditPara;

    @Schema(description = "是否翻译成英文")
    private Boolean translationFlag;

    @Schema(description = "提示词id")
    private String promptId;

    @Schema(description = "是否超200万高清")
    private Boolean highPixels;

    @Schema(description = "风格主要分类")
    private String mainCategory;

    @Schema(description = "风格次要分类")
    private String subCategory;

    @Schema(description = "忽略告警，继续生图")
    private Boolean continueCreate;

    @Schema(description = "视频生成参数")
    private VideoGenerationPara videoGenerationPara;

    @ToString
    @Data
    public static class Rembg {

        @Schema(description = "传isnet-anime")
        private String model;

        @Schema(description = "去背景相关阈值")
        private int foregroundThreshold;

        @Schema(description = "去背景相关阈值")
        private int backgroundThreshold;

        @Schema(description = "去背景相关阈值")
        private int erodeSize;

        @Schema(description = "背景颜色")
        private int backgroundColor;

        @Schema(description = "图片名称")
        private String fileName;
    }

    @ToString
    @Data
    public static class Hires_fix {

        @Schema(description = "超清修复倍数")
        private double scale;

        @Schema(description = "降噪幅度")
        private double denoise;

        @Schema(description = "图片名称")
        private String fileName;

    }

    @ToString
    @Data
    public static class Model_ability {

        @Schema(description = "0.0 - 1.0 之间，默认 0 不生效")
        private double anime_style_control;

    }

    @ToString
    @Data
    public static class Img2img_info {
        @Schema(description = "图片url")
        private String img_url;

        @Schema(description = "图片风格")
        private String style;

        @Schema(description = "图生图权重")
        private Double weight;
    }

    @ToString
    @Data
    public static class Multi_img2img_info {
        List<StyleImg> style_list;
    }

    @ToString
    @Data
    public static class Img_control_info {
        List<StyleImg> style_list;
    }

    @ToString
    @Data
    public static class StyleImg {
        @Schema(description = "图片url")
        private String img_url;

        @Schema(description = "图片风格")
        private String style;

        @Schema(description = "图生图权重")
        private Double weight;
    }

    @ToString
    @Data
    public static class ImgEditPara {
        @Schema(description = "图片id")
        private String fileId;
        @Schema(description = "图片url")
        private String imgUrl;
    }

    @ToString
    @Data
    public static class VideoGenerationPara {
        @Schema(description = "图片URL（图生视频时使用）")
        private List<PositionImg> positionImgs;

        @Schema(description = "视频类型：text_to_video（文生视频）或 image_to_video（图生视频）")
        private String video_type;

        @Schema(description = "视频时长")
        private Integer duration;
    }

    @ToString
    @Data
    public static class PositionImg {
        @Schema(description = "图片url")
        private String img_url;

        @Schema(description = "图片位置，首尾等，first，last")
        private String position;
    }
}
