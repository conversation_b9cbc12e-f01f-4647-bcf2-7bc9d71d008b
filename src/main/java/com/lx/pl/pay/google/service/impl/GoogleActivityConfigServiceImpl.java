package com.lx.pl.pay.google.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.PayLotteryLogService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.google.domain.GoogleActivityConfig;
import com.lx.pl.pay.google.dto.GoogleActivityConfigVo;
import com.lx.pl.pay.google.mapper.GoogleActivityConfigMapper;
import com.lx.pl.pay.google.service.GoogleActivityConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class GoogleActivityConfigServiceImpl extends ServiceImpl<GoogleActivityConfigMapper, GoogleActivityConfig> implements GoogleActivityConfigService {

    @Autowired
    PayLotteryLogService payLotteryLogService;

    @Autowired
    SubscriptionCurrentService subscriptionCurrentService;

    @Override
    public List<GoogleActivityConfigVo> listActivity(User user, String platform) {
        long currentTime = System.currentTimeMillis() / 1000;
        List<GoogleActivityConfig> list = this.lambdaQuery()
                .eq(GoogleActivityConfig::getEnable, true)
                .lt(GoogleActivityConfig::getStartTime, currentTime)
                .gt(GoogleActivityConfig::getDeadline, currentTime)
                .list();
        if (CollUtil.isEmpty(list)) {
            return List.of();
        }
        boolean hasVip = checkHasVip(user, subscriptionCurrentService);

        return list.stream().map(item -> {
            if (payLotteryLogService.hasParticipated(user.getId(), platform, item.getCode())) {
                return null;
            }
            if (hasVip && "sweep_stakes".equals(item.getCode())) {
                return null;
            }
            GoogleActivityConfigVo vo = new GoogleActivityConfigVo();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

    }

    public static boolean checkHasVip(User user, SubscriptionCurrentService subscriptionCurrentService) {
        boolean hasVip;
        List<SubscriptionCurrent> logicValidSubscriptionsFromDb = subscriptionCurrentService.getLogicValidSubscriptionsFromDb(user.getId());
        if (CollUtil.isNotEmpty(logicValidSubscriptionsFromDb)) {
            List<SubscriptionCurrent> collect = logicValidSubscriptionsFromDb.stream().filter(subscription -> {
                return !subscription.getVipPlatform().equalsIgnoreCase(VipPlatform.GIFT.getPlatformName());
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                hasVip = true;
            } else {
                hasVip = false;
            }
        } else {
            hasVip = false;
        }
        return hasVip;
    }

}
